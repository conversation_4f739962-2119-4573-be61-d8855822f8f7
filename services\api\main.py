"""
Main FastAPI application for the skill extractor API service.

This module sets up the FastAPI application with middleware, authentication,
health checks, metrics, and route handlers for LinkedIn and resume ingestion.
"""

# Load environment variables first, before any other imports
from dotenv import load_dotenv
load_dotenv()

import time
from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON><PERSON>ator

import uvicorn
from fastapi import FastAPI, Request, Response, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from starlette.middleware.base import BaseHTTPMiddleware

from common.settings import settings
from common.logging import configure_logging, get_logger, set_request_id, generate_request_id
from common.models import HealthCheckResponse, HealthStatus, ComponentHealth
from common.metrics import initialize_metrics, get_request_count, get_request_duration, get_ingestion_requests
from services.api.middleware import JWTMiddleware
from services.api.routes import ingest, query, health

logger = get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request logging and metrics collection."""
    
    async def dispatch(self, request: Request, call_next):
        # Generate and set request ID
        request_id = generate_request_id()
        set_request_id(request_id)
        
        # Add request ID to response headers
        start_time = time.time()
        
        # Log request start
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            request_id=request_id
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Update metrics
            get_request_count().labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=response.status_code
            ).inc()

            get_request_duration().labels(
                method=request.method,
                endpoint=request.url.path
            ).observe(duration)
            
            # Log successful request
            logger.info(
                "Request completed",
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                duration_ms=duration * 1000,
                request_id=request_id
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration = time.time() - start_time
            
            # Update metrics for errors
            get_request_count().labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=500
            ).inc()
            
            # Log error
            logger.error(
                "Request failed",
                method=request.method,
                url=str(request.url),
                error_type=type(e).__name__,
                error_message=str(e),
                duration_ms=duration * 1000,
                request_id=request_id,
                exc_info=True
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "message": "An unexpected error occurred"
                },
                headers={"X-Request-ID": request_id}
            )


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown tasks."""
    
    # Startup
    logger.info("Starting Skill Extractor API service")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")

    # Initialize metrics
    initialize_metrics()
    logger.info("Prometheus metrics initialized")

    # TODO: Initialize database connections, message queue connections, etc.

    yield
    
    # Shutdown
    logger.info("Shutting down Skill Extractor API service")
    # TODO: Cleanup connections, close resources


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    # Configure logging
    configure_logging(
        service_name="api",
        log_level=settings.log_level,
        json_logs=settings.environment == "production"
    )
    
    # Create FastAPI app
    app = FastAPI(
        title="Skill Extractor API",
        description="Backend API for extracting and vectorizing skills from LinkedIn profiles and résumé PDFs",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
    )
    
    # Add middleware
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Use settings.security.allowed_origins in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware (for production)
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # TODO: Configure allowed hosts
        )
    
    # Request logging middleware
    app.add_middleware(RequestLoggingMiddleware)
    
    # JWT authentication middleware
    app.add_middleware(JWTMiddleware)
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(ingest.router, prefix="/ingest", tags=["ingestion"])
    app.include_router(query.router, prefix="", tags=["query"])
    
    # Metrics endpoint
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint."""
        return Response(
            content=generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "skill-extractor-api",
            "version": "1.0.0",
            "status": "healthy",
            "environment": settings.environment,
            "docs_url": "/docs" if settings.debug else None
        }
    
    return app


# Create the app instance
app = create_app()


def main():
    """Main entry point for running the API service."""

    logger.info(f"Starting API server on {settings.api.host}:{settings.api.port}")

    uvicorn.run(
        "services.api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=False,  # We handle logging in middleware
        workers=1 if settings.debug else settings.workers.extraction_concurrency,
    )


if __name__ == "__main__":
    main() 